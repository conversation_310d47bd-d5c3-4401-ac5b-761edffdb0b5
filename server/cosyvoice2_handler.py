import logging
from cosyvoice.cli.cosyvoice import CosyVoice2

class CosyVoice2Handler:
    def __init__(self, model_dir: str):
        try:
            self.cosyvoice = CosyVoice2(model_dir=model_dir)
            logging.info("CosyVoice2 model loaded successfully")
        except Exception as e:
            logging.error(f"Failed to load CosyVoice2 model: {e}")
            raise

    def inference_zero_shot(self, tts_text, prompt_text, prompt_speech_16k, speed=1.0, stream=False):
        try:
            logging.info(f"Starting zero-shot inference for text: {tts_text[:50]}...")
            result = self.cosyvoice.inference_zero_shot(
                tts_text=tts_text,
                prompt_text=prompt_text,
                prompt_speech_16k=prompt_speech_16k,
                stream=stream,
                speed=speed
            )
            logging.info("Zero-shot inference completed successfully")
            # 若 result 为 list/生成器，取第一个元素
            if isinstance(result, (list, tuple)):
                return result[0]
            elif hasattr(result, '__next__'):  # 生成器
                return next(result)
            else:
                return result
        except Exception as e:
            logging.error(f"Error in zero-shot inference: {e}")
            raise