import os
import uuid
import torchaudio
from fastapi import FastAPI, Form
from fastapi.responses import JSONResponse
from minio import Minio
from cosyvoice2_handler import CosyVoice2Handler


ASSET_DIR = os.path.join(os.path.dirname(__file__), "../asset")
VOICE_SR = 16000

VOICE_CONFIG = {
    "tang_seng_1": {
        "path": "asset/（唐僧）弟子是东土大唐驾下差来，上西天拜佛求经去的.mp3",
        "prompt": "弟子是东土大唐驾下差来，上西天拜佛求经去的"
    },
    "sun_wukong_1": {
        "path": "asset/（孙悟空）嘿嘿！我乃五百年前大闹天宫的齐天大圣.mp3",
        "prompt": "嘿嘿！我乃五百年前大闹天宫的齐天大圣"
    },
    "sun_wukong_2": {
        "path": "asset/（孙悟空）见了小的要叫姑娘，见了老的要叫奶奶.mp3",
        "prompt": "见了小的要叫姑娘，见了老的要叫奶奶"
    },
    "sha_seng_1": {
        "path": "asset/（沙僧）师傅被那妖怪给捉去了.mp3",
        "prompt": "师傅被那妖怪给捉去了"
    },
    "zhu_bajie_1": {
        "path": "asset/（猪八戒）看你的得意地，一听说抓妖怪就跟见了你外公似的.mp3",
        "prompt": "看你的得意地，一听说抓妖怪就跟见了你外公似的"
    },
    "hong_haier_1": {
        "path": "asset/（红孩儿）你是猴子请来的救兵嘛.mp3",
        "prompt": "你是猴子请来的救兵嘛"
    }
}

def load_voices(voice_config):
    voices = {}
    for voice_id, info in voice_config.items():
        path = info["path"]
        if not os.path.exists(path):
            continue
        waveform, sr = torchaudio.load(path)
        if sr != VOICE_SR:
            waveform = torchaudio.functional.resample(waveform, sr, VOICE_SR)
        voices[voice_id] = waveform.mean(dim=0) if waveform.shape[0] > 1 else waveform.squeeze(0)
    return voices

def create_app(model_dir):
    app = FastAPI()
    handler = CosyVoice2Handler(model_dir)
    minio_client = Minio(
        endpoint=os.getenv("MINIO_ENDPOINT", "localhost:9000"),
        access_key=os.getenv("MINIO_ACCESS_KEY", "minioadmin"),
        secret_key=os.getenv("MINIO_SECRET_KEY", "minioadmin"),
        secure=False
    )
    minio_bucket = os.getenv("MINIO_BUCKET", "cosyvoice-audio")
    if not minio_client.bucket_exists(minio_bucket):
        minio_client.make_bucket(minio_bucket)

    voice_dict = load_voices(VOICE_CONFIG)

    @app.post("/inference")
    async def inference(
        text: str = Form(...),
        voice_id: str = Form(...)
    ):
        if voice_id not in voice_dict or voice_id not in VOICE_CONFIG:
            return JSONResponse(status_code=400, content={"error": "Invalid voice_id"})
        prompt_speech = voice_dict[voice_id]
        prompt_text = VOICE_CONFIG[voice_id]["prompt"]
        result = handler.inference_zero_shot(
            tts_text=text,
            prompt_text=prompt_text,
            prompt_speech_16k=prompt_speech,
            speed=1.0
        )
        tts_speech = result["tts_speech"] if isinstance(result, dict) else result
        audio_id = str(uuid.uuid4())
        wav_path = f"/tmp/{audio_id}.wav"
        torchaudio.save(wav_path, tts_speech.unsqueeze(0), VOICE_SR)
        with open(wav_path, "rb") as f:
            minio_client.put_object(
                minio_bucket,
                f"{audio_id}.wav",
                f,
                length=os.path.getsize(wav_path),
                content_type="audio/wav"
            )
        minio_url = f"http://{minio_client._endpoint}/{minio_bucket}/{audio_id}.wav"
        return JSONResponse(content={"audio_url": minio_url})

    return app
