import os
import argparse
import logging
from fastapi_app import create_app

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--port',
                        type=int,
                        default=50000,
                        help='server port')
    parser.add_argument('--model_dir',
                        type=str,
                        default='pretrained_models/CosyVoice2-0.5B',
                        help='local path or modelscope repo id')
    parser.add_argument('--host',
                        type=str,
                        default='0.0.0.0',
                        help='server host')
    args = parser.parse_args()

    app = create_app(model_dir=args.model_dir)
    
    import uvicorn
    uvicorn.run(app, host=args.host, port=args.port)


if __name__ == '__main__':
    main()